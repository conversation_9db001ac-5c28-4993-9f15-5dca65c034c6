[{"id": "pbc_1736455494", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "users", "type": "auth", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cost": 0, "hidden": true, "id": "password901924565", "max": 0, "min": 8, "name": "password", "pattern": "", "presentable": false, "required": true, "system": true, "type": "password"}, {"autogeneratePattern": "[a-zA-Z0-9]{50}", "hidden": true, "id": "text2504183744", "max": 60, "min": 30, "name": "<PERSON><PERSON><PERSON>", "pattern": "", "presentable": false, "primaryKey": false, "required": true, "system": true, "type": "text"}, {"exceptDomains": null, "hidden": false, "id": "email3885137012", "name": "email", "onlyDomains": null, "presentable": false, "required": true, "system": true, "type": "email"}, {"hidden": false, "id": "bool1547992806", "name": "emailVisibility", "presentable": false, "required": false, "system": true, "type": "bool"}, {"hidden": false, "id": "bool256245529", "name": "verified", "presentable": false, "required": false, "system": true, "type": "bool"}, {"autogeneratePattern": "", "hidden": false, "id": "text1579384326", "max": 0, "min": 0, "name": "name", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select1466534506", "maxSelect": 1, "name": "role", "presentable": false, "required": false, "system": false, "type": "select", "values": ["therapist", "user"]}, {"hidden": false, "id": "file376926767", "maxSelect": 1, "maxSize": 0, "mimeTypes": [], "name": "avatar", "presentable": false, "protected": false, "required": false, "system": false, "thumbs": [], "type": "file"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": ["CREATE UNIQUE INDEX `idx_tokenKey_pbc_1736455494` ON `users` (`tokenKey`)", "CREATE UNIQUE INDEX `idx_email_pbc_1736455494` ON `users` (`email`) WHERE `email` != ''"], "system": false, "authRule": "", "manageRule": null, "authAlert": {"enabled": true, "emailTemplate": {"subject": "Login from a new location", "body": "<p>Hello,</p>\n<p>We noticed a login to your {APP_NAME} account from a new location.</p>\n<p>If this was you, you may disregard this email.</p>\n<p><strong>If this wasn't you, you should immediately change your {APP_NAME} account password to revoke access from all other locations.</strong></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "oauth2": {"mappedFields": {"id": "", "name": "", "username": "", "avatarURL": ""}, "enabled": false}, "passwordAuth": {"enabled": true, "identityFields": ["email"]}, "mfa": {"enabled": false, "duration": 1800, "rule": ""}, "otp": {"enabled": false, "duration": 180, "length": 8, "emailTemplate": {"subject": "OTP for {APP_NAME}", "body": "<p>Hello,</p>\n<p>Your one-time password is: <strong>{OTP}</strong></p>\n<p><i>If you didn't ask for the one-time password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, "authToken": {"duration": 604800}, "passwordResetToken": {"duration": 1800}, "emailChangeToken": {"duration": 1800}, "verificationToken": {"duration": 259200}, "fileToken": {"duration": 180}, "verificationTemplate": {"subject": "Verify your {APP_NAME} email", "body": "<p>Hello,</p>\n<p>Thank you for joining us at {APP_NAME}.</p>\n<p>Click on the button below to verify your email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-verification/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Verify</a>\n</p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "resetPasswordTemplate": {"subject": "Reset your {APP_NAME} password", "body": "<p>Hello,</p>\n<p>Click on the button below to reset your password.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-password-reset/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Reset password</a>\n</p>\n<p><i>If you didn't ask to reset your password, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}, "confirmEmailChangeTemplate": {"subject": "Confirm your {APP_NAME} new email address", "body": "<p>Hello,</p>\n<p>Click on the button below to confirm your new email address.</p>\n<p>\n  <a class=\"btn\" href=\"{APP_URL}/_/#/auth/confirm-email-change/{TOKEN}\" target=\"_blank\" rel=\"noopener\">Confirm new email</a>\n</p>\n<p><i>If you didn't ask to change your email address, you can ignore this email.</i></p>\n<p>\n  Thanks,<br/>\n  {APP_NAME} team\n</p>"}}, {"id": "pbc_986407980", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "bookings", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_2412743827", "hidden": false, "id": "relation1139323028", "maxSelect": 1, "minSelect": 0, "name": "therapist_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"cascadeDelete": false, "collectionId": "pbc_2395140099", "hidden": false, "id": "relation2809058197", "maxSelect": 1, "minSelect": 0, "name": "user_id", "presentable": false, "required": false, "system": false, "type": "relation"}, {"hidden": false, "id": "date2862495610", "max": "", "min": "", "name": "date", "presentable": false, "required": false, "system": false, "type": "date"}, {"hidden": false, "id": "date1872009285", "max": "", "min": "", "name": "time", "presentable": false, "required": false, "system": false, "type": "date"}, {"autogeneratePattern": "", "hidden": false, "id": "text3852478864", "max": 0, "min": 0, "name": "day", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2555517871", "maxSelect": 1, "name": "status", "presentable": false, "required": false, "system": false, "type": "select", "values": ["pending", "accepted", "rejected"]}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2412743827", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "therapist_profile", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1736455494", "hidden": false, "id": "relation2375276105", "maxSelect": 1, "minSelect": 0, "name": "user", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3709889147", "max": 0, "min": 0, "name": "bio", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"autogeneratePattern": "", "hidden": false, "id": "text3071471822", "max": 0, "min": 0, "name": "qualification", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "select2745781978", "maxSelect": 7, "name": "avaliable_days", "presentable": false, "required": false, "system": false, "type": "select", "values": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}, {"hidden": false, "id": "number93372675", "max": null, "min": null, "name": "experience", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"hidden": false, "id": "json3069681343", "maxSize": 0, "name": "specializations", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}, {"id": "pbc_2395140099", "listRule": "", "viewRule": "", "createRule": "", "updateRule": "", "deleteRule": "", "name": "user_profile", "type": "base", "fields": [{"autogeneratePattern": "[a-z0-9]{15}", "hidden": false, "id": "text3208210256", "max": 15, "min": 15, "name": "id", "pattern": "^[a-z0-9]+$", "presentable": false, "primaryKey": true, "required": true, "system": true, "type": "text"}, {"cascadeDelete": false, "collectionId": "pbc_1736455494", "hidden": false, "id": "relation344172009", "maxSelect": 1, "minSelect": 0, "name": "users", "presentable": false, "required": false, "system": false, "type": "relation"}, {"autogeneratePattern": "", "hidden": false, "id": "text3343321666", "max": 0, "min": 0, "name": "gender", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "json3274439210", "maxSize": 0, "name": "concerns", "presentable": false, "required": false, "system": false, "type": "json"}, {"hidden": false, "id": "number2704281778", "max": null, "min": null, "name": "age", "onlyInt": false, "presentable": false, "required": false, "system": false, "type": "number"}, {"autogeneratePattern": "", "hidden": false, "id": "text3130199401", "max": 0, "min": 0, "name": "profession", "pattern": "", "presentable": false, "primaryKey": false, "required": false, "system": false, "type": "text"}, {"hidden": false, "id": "autodate2990389176", "name": "created", "onCreate": true, "onUpdate": false, "presentable": false, "system": false, "type": "autodate"}, {"hidden": false, "id": "autodate3332085495", "name": "updated", "onCreate": true, "onUpdate": true, "presentable": false, "system": false, "type": "autodate"}], "indexes": [], "system": false}]